/**
 * Custom Mobile Footer CSS
 * <PERSON>u dosya, mobil dashboard footer'daki elementlerin sırasını değiştirmek için kullanılır.
 *
 * @package DmrLMS
 * @since 1.0.1
 */

/* Mobil dashboard footer elementlerinin sırasını değiştir */
#tutor-dashboard-footer-mobile > div > div {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
}

/* Hamburger menü butonunu (3. element) en sola taşı */
#tutor-dashboard-footer-mobile > div > div > a:nth-child(3) {
    order: 1;
}

/* 1. elementi ortaya taşı */
#tutor-dashboard-footer-mobile > div > div > a:nth-child(1) {
    order: 2;
}

/* 2. elementi en sağa taşı */
#tutor-dashboard-footer-mobile > div > div > a:nth-child(2) {
    order: 3;
}

/* Mobil footer genel stilleri */
#tutor-dashboard-footer-mobile {
    background-color: #ffffff;
    border-top: 1px solid #e9ecef;
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index: 99;
    transition: background-color 0.3s ease, border-color 0.3s ease;
}

/* Mobil footer içindeki butonlar */
#tutor-dashboard-footer-mobile a {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 10px 0;
    color: #212327;
    text-decoration: none;
    transition: color 0.3s ease;
}

/* Mobil footer içindeki ikonlar */
#tutor-dashboard-footer-mobile a i {
    font-size: 20px;
    margin-bottom: 5px;
}

/* Mobil footer içindeki metin */
#tutor-dashboard-footer-mobile a span {
    font-size: 12px;
}

/* Aktif buton stilleri */
#tutor-dashboard-footer-mobile a.active {
    color: var(--tutor-color-primary);
}

/* Dark Mode - Mobil Footer */
body.tutor-dark-mode #tutor-dashboard-footer-mobile,
html[data-theme="dark"] #tutor-dashboard-footer-mobile {
    background-color: #121212 !important;
    border-top: 1px solid #2A2A2A !important;
}

/* Dark Mode - Mobil Footer Butonları */
body.tutor-dark-mode #tutor-dashboard-footer-mobile a,
html[data-theme="dark"] #tutor-dashboard-footer-mobile a {
    color: #f5f5f5 !important;
}

/* Dark Mode - Mobil Footer Aktif Buton */
body.tutor-dark-mode #tutor-dashboard-footer-mobile a.active,
html[data-theme="dark"] #tutor-dashboard-footer-mobile a.active {
    color: var(--tutor-color-primary) !important;
}

/* Mobil ve tablet cihazlarda buton tıklama animasyonu */
#tutor-dashboard-footer-mobile a:active {
    transform: scale(0.9);
    transition: transform 0.2s ease;
}

/* Tutor dashboard footer mobile için padding ayarı */
.tutor-dashboard #tutor-dashboard-footer-mobile {
    padding: 10px 0px 0px 0px;
}

/* Orijinal Tutor LMS medya sorgusunu geçersiz kıl */
@media (min-width: 991px) {
    .tutor-dashboard #tutor-dashboard-footer-mobile {
        display: none !important;
    }
}

/* Orijinal Tutor LMS medya sorgusunu engelle */
@media (min-width: 768px) and (max-width: 991px) {
    .tutor-dashboard #tutor-dashboard-footer-mobile {
        display: block !important;
    }
}

/* Mobil footer butonları için özel ikonlar - Sidebar ile aynı ikonlar kullanılıyor */
/* tutor-icon-book-open ve tutor-icon-quiz ikonları Tutor LMS'in standart ikonlarıdır */
/* Bu ikonlar sidebar'daki menü öğeleriyle aynıdır */

/* Mobil footer buton metinleri için responsive düzenlemeler */
@media (max-width: 480px) {
    #tutor-dashboard-footer-mobile a span {
        font-size: 11px;
        line-height: 1.2;
    }

    #tutor-dashboard-footer-mobile a {
        padding: 8px 0;
    }

    #tutor-dashboard-footer-mobile a i {
        font-size: 18px;
        margin-bottom: 3px;
    }
}

/* Çok küçük ekranlar için metin kısaltma */
@media (max-width: 360px) {
    #tutor-dashboard-footer-mobile a span {
        font-size: 10px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        max-width: 80px;
    }
}
